' Option Explicit 是 VBScript（Visual Basic Script）中的一条指令，它的作用是强制在代码中声明所有变量。具体来说，它要求你在使用变量之前，必须使用 Dim、Private、Public 或 Static 等关键字进行声明，否则代码会报错。
' Option Explicit 强制要求在使用变量之前进行声明，避免了因变量未声明或拼写错误带来的潜在问题。
' 它是一个良好的编程习惯，有助于提高代码质量和可维护性。

' 软件自启动vbs
Option Explicit

Dim objShell, commandCount, commands()
commandCount = 0
Set objShell = CreateObject("WScript.Shell")

' 确保所有路径用双引号包裹，处理空格问题
AddCommand Chr(34) & "C:\Users\<USER>\AppData\Local\Programs\utools\uTools.exe" & Chr(34)
' AddCommand Chr(34) & "E:\Everything\Everything.exe" & Chr(34)
AddCommand Chr(34) & "E:\mihomo-party-windows-1.8.4-x64-portable\Mihomo Party.exe" & Chr(34)
AddCommand Chr(34) & "E:\Snipaste-1.16.2-x86\Snipaste.exe" & Chr(34)
AddCommand Chr(34) & "E:\TrafficMonitor\TrafficMonitor.exe" & Chr(34)
AddCommand Chr(34) & "E:\ScheduleTask\Directory Opus (启动).lnk" & Chr(34)
AddCommand Chr(34) & "E:\ScheduleTask\wsl.vbs" & Chr(34)
' AddCommand Chr(34) & "C:\Program Files\WindowsApps\28017CharlesMilette.TranslucentTB_2024.3.0.0_x64__v826wp6bftszj\TranslucentTB.exe" & Chr(34)
' AddCommand Chr(34) & "C:\Users\<USER>\AppData\Local\StartAllBack\StartAllBackCfg.exe" & Chr(34)

Dim i
For i = 0 To UBound(commands)
    On Error Resume Next ' 启用错误处理
    objShell.Run commands(i), 0, False
    If Err.Number <> 0 Then
        MsgBox "启动失败：" & commands(i) & vbCrLf & "错误原因：" & Err.Description, vbCritical, "错误"
        Err.Clear
    End If
    On Error Goto 0 ' 关闭错误处理
Next

Set objShell = Nothing

Sub AddCommand(command)
    ReDim Preserve commands(commandCount)
    commands(commandCount) = command
    commandCount = commandCount + 1
End Sub